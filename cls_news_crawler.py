#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财联社新闻爬虫
用于爬取财联社深度新闻页面的新闻内容
"""

import requests
import json
import time
import random
from datetime import datetime
from typing import List, Dict, Optional
import csv
import os
from urllib.parse import urljoin, urlparse
import re


class CLSNewsCrawler:
    """财联社新闻爬虫类"""
    
    def __init__(self):
        self.base_url = "https://www.cls.cn"
        self.api_base = "https://www.cls.cn/api"
        self.session = requests.Session()
        
        # 设置请求头，模拟真实浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        self.session.headers.update(self.headers)
        
        # 分类ID映射
        self.category_map = {
            '头条': 1000,
            'A股': 1003,
            '港股': 1135,
            '环球': 1007,
            '公司': 1005,
            '券商': 1118,
            '基金·ETF': 1110,
            '地产': 1006,
            '金融': 1032,
            '汽车': 1119,
            '科创': 1111,
            '品见': 1160
        }
    
    def get_news_list(self, category_id: int = 1000, page: int = 1, page_size: int = 20) -> Optional[List[Dict]]:
        """
        获取新闻列表
        
        Args:
            category_id: 分类ID，默认1000（头条）
            page: 页码，从1开始
            page_size: 每页数量
            
        Returns:
            新闻列表或None
        """
        try:
            # 财联社的API接口（需要根据实际情况调整）
            api_url = f"{self.api_base}/depth/list"
            
            params = {
                'id': category_id,
                'page': page,
                'pageSize': page_size,
                'timestamp': int(time.time() * 1000)
            }
            
            print(f"正在获取第{page}页新闻列表...")
            response = self.session.get(api_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0:
                    return data.get('data', {}).get('list', [])
                else:
                    print(f"API返回错误: {data.get('message', '未知错误')}")
            else:
                print(f"请求失败，状态码: {response.status_code}")
                
        except requests.RequestException as e:
            print(f"请求异常: {e}")
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
        except Exception as e:
            print(f"获取新闻列表时发生错误: {e}")
            
        return None
    
    def get_news_detail(self, news_id: str) -> Optional[Dict]:
        """
        获取新闻详情
        
        Args:
            news_id: 新闻ID
            
        Returns:
            新闻详情或None
        """
        try:
            detail_url = f"{self.api_base}/depth/detail"
            params = {
                'id': news_id,
                'timestamp': int(time.time() * 1000)
            }
            
            print(f"正在获取新闻详情: {news_id}")
            response = self.session.get(detail_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0:
                    return data.get('data', {})
                else:
                    print(f"获取详情失败: {data.get('message', '未知错误')}")
            else:
                print(f"请求失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"获取新闻详情时发生错误: {e}")
            
        return None
    
    def parse_news_item(self, news_item: Dict) -> Dict:
        """
        解析新闻条目
        
        Args:
            news_item: 原始新闻数据
            
        Returns:
            解析后的新闻数据
        """
        return {
            'id': news_item.get('id', ''),
            'title': news_item.get('title', ''),
            'summary': news_item.get('summary', ''),
            'author': news_item.get('author', ''),
            'publish_time': news_item.get('publishTime', ''),
            'update_time': news_item.get('updateTime', ''),
            'category': news_item.get('category', ''),
            'tags': news_item.get('tags', []),
            'url': f"{self.base_url}/depth/{news_item.get('id', '')}",
            'image_url': news_item.get('imageUrl', ''),
            'view_count': news_item.get('viewCount', 0),
            'comment_count': news_item.get('commentCount', 0)
        }
    
    def crawl_category_news(self, category: str = '头条', max_pages: int = 5) -> List[Dict]:
        """
        爬取指定分类的新闻
        
        Args:
            category: 分类名称
            max_pages: 最大页数
            
        Returns:
            新闻列表
        """
        category_id = self.category_map.get(category, 1000)
        all_news = []
        
        print(f"开始爬取【{category}】分类新闻，最多{max_pages}页")
        
        for page in range(1, max_pages + 1):
            news_list = self.get_news_list(category_id, page)
            
            if not news_list:
                print(f"第{page}页没有获取到数据，停止爬取")
                break
                
            for news_item in news_list:
                parsed_news = self.parse_news_item(news_item)
                
                # 获取详细内容
                detail = self.get_news_detail(parsed_news['id'])
                if detail:
                    parsed_news['content'] = detail.get('content', '')
                    parsed_news['html_content'] = detail.get('htmlContent', '')
                
                all_news.append(parsed_news)
                
                # 添加延时，避免请求过快
                time.sleep(random.uniform(0.5, 1.5))
            
            print(f"第{page}页爬取完成，获取{len(news_list)}条新闻")
            
            # 页面间延时
            time.sleep(random.uniform(1, 3))
        
        print(f"爬取完成，共获取{len(all_news)}条新闻")
        return all_news
    
    def save_to_csv(self, news_list: List[Dict], filename: str = None):
        """
        保存新闻到CSV文件
        
        Args:
            news_list: 新闻列表
            filename: 文件名
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"cls_news_{timestamp}.csv"
        
        if not news_list:
            print("没有新闻数据可保存")
            return
        
        fieldnames = ['id', 'title', 'summary', 'author', 'publish_time', 
                     'category', 'url', 'view_count', 'comment_count', 'content']
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for news in news_list:
                # 只保存需要的字段
                row = {field: news.get(field, '') for field in fieldnames}
                writer.writerow(row)
        
        print(f"新闻数据已保存到: {filename}")
    
    def save_to_json(self, news_list: List[Dict], filename: str = None):
        """
        保存新闻到JSON文件
        
        Args:
            news_list: 新闻列表
            filename: 文件名
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"cls_news_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(news_list, f, ensure_ascii=False, indent=2)
        
        print(f"新闻数据已保存到: {filename}")


def main():
    """主函数"""
    crawler = CLSNewsCrawler()
    
    print("财联社新闻爬虫启动")
    print("可用分类:", list(crawler.category_map.keys()))
    
    # 爬取头条新闻
    news_list = crawler.crawl_category_news('头条', max_pages=3)
    
    if news_list:
        # 保存数据
        crawler.save_to_csv(news_list)
        crawler.save_to_json(news_list)
        
        # 显示统计信息
        print(f"\n爬取统计:")
        print(f"总新闻数: {len(news_list)}")
        print(f"最新新闻: {news_list[0]['title'] if news_list else '无'}")
    else:
        print("未获取到任何新闻数据")


if __name__ == "__main__":
    main()
