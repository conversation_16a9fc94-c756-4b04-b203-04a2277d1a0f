#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财联社深度页面爬虫
获取指定深度页面的HTML内容
"""

import requests
from bs4 import BeautifulSoup
import json
import time
from typing import Optional, Dict, Any


class CLSDepthCrawler:
    """财联社深度页面爬虫类"""
    
    def __init__(self):
        self.session = requests.Session()
        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def get_page_content(self, article_id: str, timeout: int = 30) -> Optional[Dict[str, Any]]:
        """
        获取指定ID的深度页面内容
        
        Args:
            article_id: 文章ID
            timeout: 请求超时时间（秒）
            
        Returns:
            包含页面信息的字典，如果失败返回None
        """
        url = f"https://www.cls.cn/depth?id={article_id}"
        
        try:
            print(f"正在请求URL: {url}")
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()  # 检查HTTP错误
            
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取页面基本信息
            result = {
                'url': url,
                'status_code': response.status_code,
                'title': self._extract_title(soup),
                'content': self._extract_content(soup),
                'publish_time': self._extract_publish_time(soup),
                'author': self._extract_author(soup),
                'tags': self._extract_tags(soup),
                'raw_html': response.text,
                'response_headers': dict(response.headers)
            }
            
            print(f"成功获取页面内容，标题: {result['title']}")
            return result
            
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None
        except Exception as e:
            print(f"解析页面时出错: {e}")
            return None
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取文章标题"""
        title_selectors = [
            'h1.article-title',
            'h1',
            '.title',
            'title'
        ]
        
        for selector in title_selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text().strip()
        
        return "未找到标题"
    
    def _extract_content(self, soup: BeautifulSoup) -> str:
        """提取文章正文内容"""
        content_selectors = [
            '.article-content',
            '.content',
            '.detail-content',
            'article'
        ]
        
        for selector in content_selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text().strip()
        
        return "未找到正文内容"
    
    def _extract_publish_time(self, soup: BeautifulSoup) -> str:
        """提取发布时间"""
        time_selectors = [
            '.publish-time',
            '.time',
            '.date',
            '[data-time]'
        ]
        
        for selector in time_selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text().strip()
        
        return "未找到发布时间"
    
    def _extract_author(self, soup: BeautifulSoup) -> str:
        """提取作者信息"""
        author_selectors = [
            '.author',
            '.writer',
            '.by-author'
        ]
        
        for selector in author_selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text().strip()
        
        return "未找到作者信息"
    
    def _extract_tags(self, soup: BeautifulSoup) -> list:
        """提取标签"""
        tags = []
        tag_selectors = [
            '.tags a',
            '.tag',
            '.keywords'
        ]
        
        for selector in tag_selectors:
            elements = soup.select(selector)
            for element in elements:
                tag_text = element.get_text().strip()
                if tag_text and tag_text not in tags:
                    tags.append(tag_text)
        
        return tags
    
    def save_to_file(self, data: Dict[str, Any], filename: str = None) -> str:
        """
        将数据保存到文件
        
        Args:
            data: 要保存的数据
            filename: 文件名，如果不指定则自动生成
            
        Returns:
            保存的文件路径
        """
        if filename is None:
            timestamp = int(time.time())
            filename = f"cls_depth_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"数据已保存到: {filename}")
        return filename


def main():
    """主函数示例"""
    crawler = CLSDepthCrawler()
    
    # 示例：获取ID为1000的深度页面
    article_id = "1000"
    result = crawler.get_page_content(article_id)
    
    if result:
        print("\n=== 页面信息 ===")
        print(f"URL: {result['url']}")
        print(f"状态码: {result['status_code']}")
        print(f"标题: {result['title']}")
        print(f"发布时间: {result['publish_time']}")
        print(f"作者: {result['author']}")
        print(f"标签: {result['tags']}")
        print(f"内容预览: {result['content'][:200]}...")
        
        # 保存到文件
        crawler.save_to_file(result)
    else:
        print("获取页面内容失败")


if __name__ == "__main__":
    main()
