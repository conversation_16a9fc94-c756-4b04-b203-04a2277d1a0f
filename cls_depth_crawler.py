#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财联社深度页面爬虫
获取指定深度页面的HTML内容
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import re
from typing import Optional, Dict, Any, List


class CLSDepthCrawler:
    """财联社深度页面爬虫类"""
    
    def __init__(self):
        self.session = requests.Session()
        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def get_page_content(self, article_id: str, timeout: int = 30) -> Optional[Dict[str, Any]]:
        """
        获取指定ID的深度页面内容

        Args:
            article_id: 文章ID
            timeout: 请求超时时间（秒）

        Returns:
            包含页面信息的字典，如果失败返回None
        """
        url = f"https://www.cls.cn/depth?id={article_id}"

        try:
            print(f"正在请求URL: {url}")
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()  # 检查HTTP错误

            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取Next.js数据
            next_data = self._extract_next_data(soup)

            # 获取文章列表数据
            articles = self._get_articles_from_api(article_id, timeout)

            # 提取页面基本信息
            result = {
                'url': url,
                'status_code': response.status_code,
                'title': self._extract_title(soup),
                'next_data': next_data,
                'articles': articles,
                'category_info': self._get_category_info(next_data, article_id),
                'raw_html': response.text,
                'response_headers': dict(response.headers)
            }

            print(f"成功获取页面内容，分类: {result['category_info']['name']}")
            print(f"获取到 {len(articles)} 篇文章")
            return result

        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None
        except Exception as e:
            print(f"解析页面时出错: {e}")
            return None
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取页面标题"""
        title_element = soup.find('title')
        if title_element:
            return title_element.get_text().strip()
        return "未找到标题"

    def _extract_next_data(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """提取Next.js数据"""
        script_tag = soup.find('script', {'id': '__NEXT_DATA__'})
        if script_tag:
            try:
                return json.loads(script_tag.string)
            except json.JSONDecodeError:
                print("解析Next.js数据失败")
                return {}
        return {}

    def _get_category_info(self, next_data: Dict[str, Any], article_id: str) -> Dict[str, Any]:
        """从Next.js数据中获取分类信息"""
        try:
            depth_nav = next_data.get('props', {}).get('initialState', {}).get('depth', {}).get('depthNavType', [])
            for nav in depth_nav:
                if str(nav.get('id')) == str(article_id):
                    return {
                        'id': nav.get('id'),
                        'name': nav.get('name'),
                        'check': nav.get('check')
                    }
        except Exception as e:
            print(f"获取分类信息失败: {e}")

        return {'id': article_id, 'name': '未知分类', 'check': 0}

    def _get_articles_from_api(self, article_id: str, timeout: int = 30) -> List[Dict[str, Any]]:
        """通过API获取文章列表"""
        api_url = "https://www.cls.cn/api/sw"

        params = {
            'app': 'CailianpressWeb',
            'os': 'web',
            'sv': '7.7.5',
            'sign': self._generate_sign(),
        }

        data = {
            'category': article_id,
            'last_time': 0,
            'refresh_type': 1,
            'rn': 20
        }

        try:
            print(f"正在请求API获取文章列表...")
            response = self.session.post(api_url, params=params, json=data, timeout=timeout)
            response.raise_for_status()

            api_result = response.json()
            if api_result.get('errno') == 0:
                articles = api_result.get('data', {}).get('roll_data', [])
                print(f"API返回 {len(articles)} 篇文章")
                return articles
            else:
                print(f"API返回错误: {api_result.get('errmsg', '未知错误')}")
                return []

        except Exception as e:
            print(f"API请求失败: {e}")
            return []

    def _generate_sign(self) -> str:
        """生成API签名（简化版本）"""
        # 这里应该实现真实的签名算法，目前使用固定值
        return "b4a875d3dd26c0c2c0d27b8d8e5e8e8e"
    
    def save_to_file(self, data: Dict[str, Any], filename: str = None) -> str:
        """
        将数据保存到文件
        
        Args:
            data: 要保存的数据
            filename: 文件名，如果不指定则自动生成
            
        Returns:
            保存的文件路径
        """
        if filename is None:
            timestamp = int(time.time())
            filename = f"cls_depth_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"数据已保存到: {filename}")
        return filename


def main():
    """主函数示例"""
    crawler = CLSDepthCrawler()

    # 示例：获取ID为1000的深度页面（头条）
    article_id = "1000"
    result = crawler.get_page_content(article_id)

    if result:
        print("\n=== 页面信息 ===")
        print(f"URL: {result['url']}")
        print(f"状态码: {result['status_code']}")
        print(f"页面标题: {result['title']}")
        print(f"分类信息: {result['category_info']}")
        print(f"文章数量: {len(result['articles'])}")

        # 显示前几篇文章的信息
        if result['articles']:
            print("\n=== 最新文章 ===")
            for i, article in enumerate(result['articles'][:5]):  # 显示前5篇
                print(f"{i+1}. {article.get('title', '无标题')}")
                print(f"   时间: {article.get('ctime', '未知时间')}")
                print(f"   摘要: {article.get('brief', '无摘要')[:100]}...")
                print()

        # 保存到文件
        crawler.save_to_file(result)
    else:
        print("获取页面内容失败")


if __name__ == "__main__":
    main()
