#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug版本 - 分析财联社深度页面
"""

import requests
from bs4 import BeautifulSoup
import json
import re
from typing import Optional, Dict, Any


def debug_cls_page():
    """Debug分析财联社页面"""
    url = "https://www.cls.cn/depth?id=1000"
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    }
    
    try:
        print(f"正在请求: {url}")
        response = requests.get(url, headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"请求失败，状态码: {response.status_code}")
            return
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 1. 提取页面标题
        title = soup.find('title')
        print(f"\n1. 页面标题: {title.get_text().strip() if title else '未找到'}")
        
        # 2. 查找Next.js数据
        next_data_script = soup.find('script', {'id': '__NEXT_DATA__'})
        if next_data_script:
            print("\n2. 找到 __NEXT_DATA__ 脚本")
            try:
                next_data = json.loads(next_data_script.string)
                print("   - Next.js数据解析成功")
                
                # 分析depth相关数据
                depth_data = next_data.get('props', {}).get('initialState', {}).get('depth', {})
                if depth_data:
                    print("   - 找到depth数据:")
                    nav_types = depth_data.get('depthNavType', [])
                    for nav in nav_types:
                        print(f"     * {nav.get('name')} (ID: {nav.get('id')})")
                
                # 查看页面查询参数
                query = next_data.get('query', {})
                print(f"   - 页面查询参数: {query}")
                
            except json.JSONDecodeError as e:
                print(f"   - Next.js数据解析失败: {e}")
        else:
            print("\n2. 未找到 __NEXT_DATA__ 脚本")
        
        # 3. 查找文章内容容器
        print("\n3. 查找文章内容容器:")
        content_containers = [
            '.depth-list-box',
            '.article-list',
            '.content-list',
            '[class*="list"]'
        ]
        
        for selector in content_containers:
            elements = soup.select(selector)
            if elements:
                print(f"   - 找到 {selector}: {len(elements)} 个元素")
                for i, elem in enumerate(elements[:2]):  # 只显示前2个
                    print(f"     [{i}] 类名: {elem.get('class', [])}")
                    print(f"     [{i}] 内容长度: {len(elem.get_text().strip())} 字符")
                    if elem.get_text().strip():
                        print(f"     [{i}] 内容预览: {elem.get_text().strip()[:100]}...")
        
        # 4. 分析页面中的JavaScript文件
        print("\n4. 页面中的JavaScript文件:")
        scripts = soup.find_all('script', src=True)
        for script in scripts[:5]:  # 只显示前5个
            src = script.get('src')
            if 'depth' in src or 'pages' in src:
                print(f"   - {src}")
        
        # 5. 查找可能的API端点
        print("\n5. 查找可能的API信息:")
        script_content = response.text
        
        # 查找API相关的URL
        api_patterns = [
            r'api["\']?\s*:\s*["\']([^"\']+)["\']',
            r'["\']([^"\']*api[^"\']*)["\']',
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'axios\.[get|post]+\s*\(\s*["\']([^"\']+)["\']'
        ]
        
        found_apis = set()
        for pattern in api_patterns:
            matches = re.findall(pattern, script_content, re.IGNORECASE)
            for match in matches:
                if 'api' in match.lower() and len(match) > 5:
                    found_apis.add(match)
        
        if found_apis:
            print("   可能的API端点:")
            for api in list(found_apis)[:10]:  # 只显示前10个
                print(f"   - {api}")
        else:
            print("   未找到明显的API端点")
        
        # 6. 检查是否有异步加载的迹象
        print("\n6. 异步加载分析:")
        if 'depth-list-box' in response.text:
            print("   - 找到 depth-list-box 容器")
        
        if 'loading' in response.text.lower():
            print("   - 页面包含loading相关内容")
        
        if 'fetch' in response.text or 'axios' in response.text:
            print("   - 页面使用了异步请求库")
        
        print(f"\n7. HTML总长度: {len(response.text)} 字符")
        
    except Exception as e:
        print(f"Debug过程中出错: {e}")


if __name__ == "__main__":
    debug_cls_page()
