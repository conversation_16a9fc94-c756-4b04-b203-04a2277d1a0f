#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试 - 财联社页面请求
"""

import requests
import json
from bs4 import BeautifulSoup

def simple_test():
    url = "https://www.cls.cn/depth?id=1000"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        print("开始请求...")
        response = requests.get(url, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 获取标题
        title = soup.find('title')
        if title:
            print(f"标题: {title.get_text()}")
        
        # 查找Next.js数据
        next_script = soup.find('script', {'id': '__NEXT_DATA__'})
        if next_script:
            print("找到Next.js数据")
            try:
                data = json.loads(next_script.string)
                # 查看depth导航数据
                depth_nav = data.get('props', {}).get('initialState', {}).get('depth', {}).get('depthNavType', [])
                print(f"导航分类数量: {len(depth_nav)}")
                for nav in depth_nav[:3]:  # 显示前3个
                    print(f"  - {nav.get('name')} (ID: {nav.get('id')})")
            except:
                print("Next.js数据解析失败")
        
        # 查找文章容器
        list_box = soup.find(class_='depth-list-box')
        if list_box:
            print(f"找到文章容器，内容长度: {len(list_box.get_text())}")
        else:
            print("未找到文章容器")
            
    except requests.exceptions.Timeout:
        print("请求超时")
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
    except Exception as e:
        print(f"其他错误: {e}")

if __name__ == "__main__":
    simple_test()
